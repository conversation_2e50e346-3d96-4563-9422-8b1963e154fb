import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
	const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;

	// Get the EntityController class
	const EntityController = AssemblyCSharp.class("EntityController");

	console.log("[+] EntityController Auto-Complete Script Loaded");
	console.log(`[+] EntityController class found: ${EntityController.handle}`);

	// Track active EntityController instances
	const activeGoodyHuts = new Set<Il2Cpp.Object>();

	// Helper function to safely call methods with error handling
	function safeMethodCall(instance: Il2Cpp.Object, methodName: string, ...args: any[]): any {
		try {
			const method = instance.method(methodName);
			if (method) {
				return method.invoke(...args);
			} else {
				console.log(`[-] Method ${methodName} not found on instance`);
				return null;
			}
		} catch (error) {
			console.log(`[-] Error calling ${methodName}: ${error}`);
			return null;
		}
	}

	console.log("[+] Entity<PERSON>ontroller auto-completion script fully initialized!");
});
