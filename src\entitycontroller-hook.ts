/**
 * Frida TypeScript Hook for Unity IL2CPP EntityController Class
 * Target: libil2cpp.so
 * Class: EntityController (empty namespace)
 * Assembly: Assembly-CSharp.dll
 */

import "frida-il2cpp-bridge";

interface EntityControllerMethods {
    IsSelected: Il2Cpp.Method | null;
    CanUpgrade: Il2Cpp.Method | null;
    GetLevel: Il2Cpp.Method | null;
    GetMaxLevel: Il2Cpp.Method | null;
    GetMaxUpgradeLevel: Il2Cpp.Method | null;
    InstantUpgrade: Il2Cpp.Method | null;
    Select: Il2Cpp.Method | null;
    Unselect: Il2Cpp.Method | null;
    GetUniqueId: Il2Cpp.Method | null;
}

interface AutomationStats {
    totalInstances: number;
    selectedInstances: number;
    upgradeableInstances: number;
    upgradesPerformed: number;
    startTime: number;
}

class EntityControllerHook {
    private assemblyImage: Il2Cpp.Image | null = null;
    private entityControllerClass: Il2Cpp.Class | null = null;
    private methods: EntityControllerMethods = {
        IsSelected: null,
        CanUpgrade: null,
        GetLevel: null,
        GetMaxLevel: null,
        GetMaxUpgradeLevel: null,
        InstantUpgrade: null,
        Select: null,
        Unselect: null,
        GetUniqueId: null
    };
    private isHooked: boolean = false;
    private stats: AutomationStats = {
        totalInstances: 0,
        selectedInstances: 0,
        upgradeableInstances: 0,
        upgradesPerformed: 0,
        startTime: Date.now()
    };

    constructor() {
        console.log("🚀 Starting EntityController IL2CPP Hook (TypeScript)...");
    }

    /**
     * Initialize IL2CPP and find EntityController class
     */
    public async initialize(): Promise<boolean> {
        try {
            console.log("🔍 Initializing IL2CPP domain...");

            // Get Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                this.listAvailableAssemblies();
                return false;
            }

            console.log("✅ Assembly-CSharp found");

            // Find EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not found");
                this.listAvailableClasses();
                return false;
            }

            console.log("✅ EntityController class found");
            console.log(`📋 Class info: ${this.entityControllerClass.name}`);

            // Setup method hooks
            this.setupMethods();

            // Setup automation functions
            this.setupGlobalFunctions();

            this.isHooked = true;
            console.log("🎯 EntityController hook setup complete!");

            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Setup method references and hooks
     */
    private setupMethods(): void {
        try {
            console.log("🔧 Setting up method references...");

            // Core methods
            this.methods.IsSelected = this.entityControllerClass!.method("IsSelected");
            this.methods.CanUpgrade = this.entityControllerClass!.method("CanUpgrade");
            this.methods.GetLevel = this.entityControllerClass!.method("GetLevel");
            this.methods.GetMaxLevel = this.entityControllerClass!.method("GetMaxLevel");
            this.methods.GetMaxUpgradeLevel = this.entityControllerClass!.method("GetMaxUpgradeLevel");
            this.methods.InstantUpgrade = this.entityControllerClass!.method("InstantUpgrade");

            // Additional methods
            try {
                this.methods.Select = this.entityControllerClass!.method("Select");
                this.methods.Unselect = this.entityControllerClass!.method("Unselect");
                this.methods.GetUniqueId = this.entityControllerClass!.method("get_uniqueId");
            } catch (error) {
                console.log(`⚠️ Some optional methods not found: ${error}`);
            }

            // Verify methods found
            Object.entries(this.methods).forEach(([methodName, method]) => {
                if (method) {
                    console.log(`✅ Found method: ${methodName}`);
                } else {
                    console.log(`⚠️ Method not found: ${methodName}`);
                }
            });

            // Hook InstantUpgrade for monitoring
            this.hookInstantUpgrade();

        } catch (error) {
            console.log(`❌ Method setup failed: ${error}`);
        }
    }

    /**
     * Hook InstantUpgrade method for monitoring
     */
    private hookInstantUpgrade(): void {
        if (!this.methods.InstantUpgrade) {
            console.log("⚠️ InstantUpgrade method not available for hooking");
            return;
        }

        try {
            // Use Interceptor.attach for safer hooking without recursion issues
            const methodAddress = this.methods.InstantUpgrade.handle;

            Interceptor.attach(methodAddress, {
                onEnter: function(args) {
                    try {
                        // args[0] is 'this' pointer for the EntityController instance
                        const thisPtr = args[0];

                        // Create Il2Cpp object from pointer to call methods
                        const entityObj = new Il2Cpp.Object(thisPtr);
                        const thisEntity = entityObj as any;

                        // Get entity info before upgrade
                        const entityId = thisEntity.get_uniqueId();
                        const levelBefore = thisEntity.GetLevel();
                        console.log(`⚡ InstantUpgrade called on entity ${entityId} (level ${levelBefore})`);

                        // Store info for onLeave
                        this.entityId = entityId;
                        this.levelBefore = levelBefore;
                        this.entityObj = entityObj;
                    } catch (error) {
                        console.log(`❌ Error in InstantUpgrade onEnter: ${error}`);
                    }
                },
                onLeave: function(retval) {
                    try {
                        if (this.entityObj && this.entityId !== undefined) {
                            const thisEntity = this.entityObj as any;
                            const levelAfter = thisEntity.GetLevel();
                            console.log(`✅ Entity ${this.entityId} upgraded: ${this.levelBefore} → ${levelAfter}`);
                        }
                    } catch (error) {
                        console.log(`❌ Error in InstantUpgrade onLeave: ${error}`);
                    }
                }
            });

            console.log("✅ InstantUpgrade method hooked with Interceptor");
        } catch (error) {
            console.log(`❌ Failed to hook InstantUpgrade: ${error}`);
        }
    }

    /**
     * Get all EntityController instances
     */
    public getAllInstances(): Il2Cpp.Object[] {
        try {
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not initialized");
                return [];
            }

            const instances = Il2Cpp.gc.choose(this.entityControllerClass);
            this.stats.totalInstances = instances.length;
            console.log(`🔍 Found ${instances.length} EntityController instances`);
            return instances;
        } catch (error) {
            console.log(`❌ Failed to get instances: ${error}`);
            return [];
        }
    }

    /**
     * Auto-upgrade ALL upgradeable entities with batch processing and anti-debugging protection
     */
    public autoUpgradeSelected(): number {
        try {
            console.log("🚀 Starting auto-upgrade for ALL upgradeable entities...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let upgradeableCount = 0;
            let validInstances = 0;
            let invalidInstances = 0;

            console.log(`🔍 Processing ${instances.length} EntityController instances in batches...`);

            // Process instances in batches to reduce anti-debugging detection
            const batchSize = 50;
            const totalBatches = Math.ceil(instances.length / batchSize);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchStart = batchIndex * batchSize;
                const batchEnd = Math.min(batchStart + batchSize, instances.length);
                const batch = instances.slice(batchStart, batchEnd);

                console.log(`📦 Processing batch ${batchIndex + 1}/${totalBatches} (instances ${batchStart}-${batchEnd - 1})`);

                batch.forEach((instance, batchLocalIndex) => {
                    const globalIndex = batchStart + batchLocalIndex;

                    try {
                        // Step 1: Validate instance before any method calls
                        if (!this.isValidInstance(instance)) {
                            invalidInstances++;
                            if (globalIndex < 5) { // Log first few invalid instances for debugging
                                console.log(`⚠️ Instance ${globalIndex} is invalid, skipping`);
                            }
                            return;
                        }

                        validInstances++;

                        // Step 2: Check if entity can be upgraded (universal check, no selection required)
                        const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
                        if (!canUpgradeMethod) {
                            if (validInstances <= 5) { // Log first few method lookup failures
                                console.log(`⚠️ Instance ${globalIndex}: CanUpgrade method not accessible`);
                            }
                            return;
                        }

                        // Step 3: Check if entity can be upgraded using alternate resources
                        const canUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${globalIndex} CanUpgrade(true)`);
                        if (canUpgrade === null) {
                            return; // Method call failed, already logged
                        }

                        if (canUpgrade) {
                            upgradeableCount++;
                            console.log(`🎯 Processing upgradeable entity ${globalIndex} (${upgradeableCount} upgradeable so far)`);

                            // Step 4: Validate upgrade capability and perform upgrades
                            const upgradeResult = this.safeUpgradeEntity(instance, globalIndex);
                            if (upgradeResult > 0) {
                                upgradedCount += upgradeResult;
                            }
                        } else if (validInstances <= 10) {
                            // Log first few non-upgradeable entities for debugging
                            console.log(`📋 Instance ${globalIndex}: Cannot be upgraded`);
                        }

                    } catch (error) {
                        const errorMsg = error instanceof Error ? error.message : String(error);
                        const errorStack = error instanceof Error ? error.stack : 'No stack trace';
                        console.log(`❌ Error processing entity ${globalIndex}: ${errorMsg}`);
                        console.log(`📋 Error details: ${errorStack}`);
                    }
                });

                // Delay between batches to reduce anti-debugging detection
                if (batchIndex < totalBatches - 1) {
                    console.log(`⏳ Batch ${batchIndex + 1} complete, waiting 500ms before next batch...`);
                    const start = Date.now();
                    while (Date.now() - start < 500) { /* 500ms delay */ }
                }
            }

            // Comprehensive summary
            console.log(`📊 Processing Summary:`);
            console.log(`   Total instances found: ${instances.length}`);
            console.log(`   Valid instances: ${validInstances}`);
            console.log(`   Invalid instances: ${invalidInstances}`);
            console.log(`   Upgradeable instances: ${upgradeableCount}`);
            console.log(`   Total upgrades performed: ${upgradedCount}`);

            if (invalidInstances > 0) {
                console.log(`⚠️ Warning: ${invalidInstances} invalid instances detected (likely destroyed objects)`);
            }

            if (validInstances > 0 && upgradeableCount === 0) {
                console.log(`💡 Info: No entities can be upgraded at this time (may be at max level or lack resources).`);
            }

            this.stats.selectedInstances = upgradeableCount; // Track upgradeable instead of selected
            this.stats.upgradesPerformed += upgradedCount;

            console.log(`✅ Auto-upgrade complete! Upgradeable: ${upgradeableCount}, Upgraded: ${upgradedCount} levels`);
            return upgradedCount;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : 'No stack trace';
            console.log(`❌ Auto-upgrade failed: ${errorMsg}`);
            console.log(`📋 Error details: ${errorStack}`);
            return 0;
        }
    }

    /**
     * Validate if an IL2CPP instance is safe to use with comprehensive testing
     */
    private isValidInstance(instance: Il2Cpp.Object): boolean {
        try {
            if (!instance) {
                return false;
            }

            // Check if instance handle is valid
            if (!instance.handle || instance.handle.isNull()) {
                return false;
            }

            // Try to access the class - this will fail for destroyed objects
            const instanceClass = instance.class;
            if (!instanceClass) {
                return false;
            }

            // Verify it's actually an EntityController
            if (instanceClass.name !== "EntityController") {
                return false;
            }

            // CRITICAL: Test method accessibility to filter destroyed objects
            // This is the key test that was missing - many objects have valid handles
            // but their methods are no longer accessible
            try {
                const testMethod = instance.method("IsSelected");
                if (!testMethod || !testMethod.handle || testMethod.handle.isNull()) {
                    return false;
                }

                // Additional method accessibility test
                const testMethod2 = instance.method("GetLevel");
                if (!testMethod2 || !testMethod2.handle || testMethod2.handle.isNull()) {
                    return false;
                }

                // Test if we can safely access the method without triggering anti-debug
                // by checking the method's implementation pointer
                if (testMethod.handle.readPointer().isNull()) {
                    return false;
                }

            } catch (methodError) {
                // Method access failed - this instance is destroyed/invalid
                return false;
            }

            return true;
        } catch (error) {
            // Any exception means the instance is invalid
            return false;
        }
    }

    /**
     * Safely get a method from an instance with validation
     */
    private safeGetMethod(instance: Il2Cpp.Object, methodName: string): Il2Cpp.Method | null {
        try {
            if (!this.isValidInstance(instance)) {
                return null;
            }

            const method = instance.method(methodName);
            if (!method) {
                return null;
            }

            // Verify method handle is valid
            if (!method.handle || method.handle.isNull()) {
                return null;
            }

            return method;
        } catch (error) {
            return null;
        }
    }

    /**
     * Safely invoke a method with basic error handling (no anti-debug complexity)
     */
    private safeInvokeMethod(method: Il2Cpp.Method, args: any[] = [], context: string = "Unknown"): any {
        try {
            if (!method) {
                console.log(`⚠️ ${context}: Method is null`);
                return null;
            }

            const result = method.invoke(...args);
            return result;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ ${context}: Method invocation failed - ${errorMsg}`);
            return null;
        }
    }

    /**
     * Safely upgrade an entity with proper level bounds checking and simplified logic
     */
    private safeUpgradeEntity(instance: Il2Cpp.Object, instanceIndex: number): number {
        try {
            let upgradedCount = 0;

            // Get all required methods safely
            const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
            const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
            const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
            const getMaxLevelMethod = this.safeGetMethod(instance, "GetMaxLevel");
            const instantUpgradeMethod = this.safeGetMethod(instance, "InstantUpgrade");

            if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod || !instantUpgradeMethod) {
                console.log(`⚠️ Instance ${instanceIndex}: Required upgrade methods not accessible`);
                return 0;
            }

            // Get initial level information
            let currentLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${instanceIndex} GetLevel (initial)`);
            const maxUpgradeLevel = this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], `Instance ${instanceIndex} GetMaxUpgradeLevel`);
            const maxLevel = getMaxLevelMethod ? this.safeInvokeMethod(getMaxLevelMethod, [], `Instance ${instanceIndex} GetMaxLevel`) : maxUpgradeLevel;

            if (currentLevel === null || maxUpgradeLevel === null) {
                console.log(`❌ Instance ${instanceIndex}: Failed to get level information`);
                return 0;
            }

            // Use the higher max level but stop at max-1 (leave final upgrade for manual)
            const trueMaxLevel = Math.max(maxUpgradeLevel || 0, maxLevel || 0);
            const actualMaxLevel = trueMaxLevel - 1; // Stop one level below maximum

            console.log(`📊 Instance ${instanceIndex}: Starting upgrade from level ${currentLevel}/${actualMaxLevel} (stopping at max-1 for manual final upgrade)`);
            console.log(`📋 Instance ${instanceIndex}: GetMaxUpgradeLevel=${maxUpgradeLevel}, GetMaxLevel=${maxLevel}, TrueMax=${trueMaxLevel}, AutoUpgradeTo=${actualMaxLevel}`);

            // Check if already at target level (max-1)
            if (currentLevel >= actualMaxLevel) {
                console.log(`� Instance ${instanceIndex}: Building at target level ${currentLevel}/${actualMaxLevel} (ready for manual final upgrade to ${trueMaxLevel})`);
                return 0;
            }

            // Check if can upgrade using alternate resource (premium upgrades)
            const canUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${instanceIndex} CanUpgrade(true)`);
            if (!canUpgrade) {
                console.log(`📋 Instance ${instanceIndex}: Cannot be upgraded (CanUpgrade returned false)`);
                return 0;
            }

            // Simple upgrade loop with proper bounds checking
            let upgradeAttempts = 0;

            // Use simple while loop with proper termination conditions
            while (currentLevel < actualMaxLevel && upgradeAttempts < actualMaxLevel) {
                // Pre-upgrade safety check
                if (currentLevel >= actualMaxLevel) {
                    console.log(`� Instance ${instanceIndex}: Building at max level ${currentLevel}/${actualMaxLevel}, stopping upgrades`);
                    break;
                }

                // Re-check if can still upgrade
                const stillCanUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${instanceIndex} CanUpgrade(true) (attempt ${upgradeAttempts + 1})`);
                if (!stillCanUpgrade) {
                    console.log(`⚠️ Instance ${instanceIndex}: CanUpgrade returned false, stopping upgrades`);
                    break;
                }

                const levelBefore = currentLevel;
                console.log(`⚡ Instance ${instanceIndex}: Calling InstantUpgrade (attempt ${upgradeAttempts + 1}) - Level ${levelBefore}/${actualMaxLevel}`);

                // Call InstantUpgrade - don't track return value, just count the call
                this.safeInvokeMethod(instantUpgradeMethod, [], `Instance ${instanceIndex} InstantUpgrade (attempt ${upgradeAttempts + 1})`);

                // Count every InstantUpgrade call (void method, ignore return value)
                upgradedCount++;

                // Wait for upgrade to process
                const upgradeProcessDelay = 500;
                const start = Date.now();
                while (Date.now() - start < upgradeProcessDelay) { /* 500ms delay */ }

                // Get new level after upgrade
                const newLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${instanceIndex} GetLevel (after upgrade ${upgradeAttempts + 1})`);
                if (newLevel === null) {
                    console.log(`⚠️ Instance ${instanceIndex}: Failed to get level after upgrade, stopping`);
                    break;
                }

                // Update current level
                currentLevel = newLevel;

                // Log the level change
                if (newLevel > levelBefore) {
                    console.log(`✅ Instance ${instanceIndex}: Level increased! ${levelBefore} → ${newLevel}`);
                } else if (newLevel === levelBefore) {
                    console.log(`⚠️ Instance ${instanceIndex}: Level unchanged (${levelBefore}), but InstantUpgrade was called`);
                } else {
                    console.log(`❌ Instance ${instanceIndex}: Level decreased (${levelBefore} → ${newLevel}), unexpected!`);
                }

                // Over-leveling protection
                if (newLevel > actualMaxLevel) {
                    console.log(`⚠️ WARNING: Instance ${instanceIndex}: Building over-leveled to ${newLevel}, max is ${actualMaxLevel}`);
                    break;
                }

                // Check if we've reached target level (max-1, leaving final upgrade for manual)
                if (newLevel >= actualMaxLevel) {
                    console.log(`🎯 Instance ${instanceIndex}: Reached target level ${newLevel}/${actualMaxLevel} (final upgrade to ${trueMaxLevel} left for manual)`);
                    break;
                }

                upgradeAttempts++;
            }

            console.log(`📊 Instance ${instanceIndex}: Auto-upgrade complete. Final level: ${currentLevel}/${actualMaxLevel} (manual upgrade to ${trueMaxLevel} available), InstantUpgrade calls: ${upgradedCount}`);
            return upgradedCount;

        } catch (error) {
            console.log(`❌ Instance ${instanceIndex}: Upgrade process failed - ${error}`);
            return 0;
        }
    }

    /**
     * Get entity information using safe method calls
     */
    public getEntityInfo(instance: Il2Cpp.Object): any {
        try {
            if (!instance) {
                console.log("❌ No instance provided");
                return null;
            }

            // Validate instance first
            if (!this.isValidInstance(instance)) {
                console.log("❌ Invalid instance provided");
                return null;
            }

            // Use safe method calls
            const isSelectedMethod = this.safeGetMethod(instance, "IsSelected");
            const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
            const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
            const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
            const getUniqueIdMethod = this.safeGetMethod(instance, "get_uniqueId");

            const info = {
                isValid: true,
                isSelected: isSelectedMethod ? this.safeInvokeMethod(isSelectedMethod, [], "GetEntityInfo IsSelected") : false,
                canUpgrade: canUpgradeMethod ? this.safeInvokeMethod(canUpgradeMethod, [false], "GetEntityInfo CanUpgrade") : false,
                currentLevel: getLevelMethod ? this.safeInvokeMethod(getLevelMethod, [], "GetEntityInfo GetLevel") : 0,
                maxLevel: getMaxUpgradeLevelMethod ? this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], "GetEntityInfo GetMaxUpgradeLevel") : 0,
                uniqueId: getUniqueIdMethod ? this.safeInvokeMethod(getUniqueIdMethod, [], "GetEntityInfo get_uniqueId") : "unknown"
            };

            // Handle null returns from safe method calls
            if (info.isSelected === null) info.isSelected = false;
            if (info.canUpgrade === null) info.canUpgrade = false;
            if (info.currentLevel === null) info.currentLevel = 0;
            if (info.maxLevel === null) info.maxLevel = 0;
            if (info.uniqueId === null) info.uniqueId = "unknown";

            console.log(`📋 Entity Info: ID=${info.uniqueId}, Selected=${info.isSelected}, Level=${info.currentLevel}/${info.maxLevel}, CanUpgrade=${info.canUpgrade}`);
            return info;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Failed to get entity info: ${errorMsg}`);
            return {
                isValid: false,
                error: errorMsg
            };
        }
    }

    /**
     * Setup global automation functions
     */
    private setupGlobalFunctions(): void {
        console.log("🤖 Setting up global automation functions...");

        // Make methods available globally using globalThis (proper Frida global scope)
        try {
            (globalThis as any).getAllEntityInstances = () => this.getAllInstances();
            (globalThis as any).autoUpgradeSelected = () => this.autoUpgradeSelected();
            (globalThis as any).getEntityInfo = (instance: Il2Cpp.Object) => this.getEntityInfo(instance);
            (globalThis as any).getEntityStats = () => this.getStats();
            (globalThis as any).forceUpgradeSelected = () => this.forceUpgradeSelected();
            (globalThis as any).debugUpgradeFailure = (instanceIndex: number) => this.debugUpgradeFailure(instanceIndex);
            (globalThis as any).slowUpgradeSelected = () => this.slowUpgradeSelected();
            (globalThis as any).simpleUpgradeSelected = () => this.simpleUpgradeSelected();

            console.log("✅ Global functions assigned to globalThis");
        } catch (error) {
            console.log(`⚠️ globalThis assignment failed, trying alternative: ${error}`);

            // Fallback: try direct assignment to global scope
            try {
                (global as any).getAllEntityInstances = () => this.getAllInstances();
                (global as any).autoUpgradeSelected = () => this.autoUpgradeSelected();
                (global as any).getEntityInfo = (instance: Il2Cpp.Object) => this.getEntityInfo(instance);
                (global as any).getEntityStats = () => this.getStats();

                console.log("✅ Global functions assigned to global");
            } catch (globalError) {
                console.log(`⚠️ global assignment also failed: ${globalError}`);
                console.log("💡 Functions will be available through entityHook instance");
            }
        }

        console.log("✅ Global functions setup complete!");
        console.log("📋 Available functions:");
        console.log("   - getAllEntityInstances()");
        console.log("   - autoUpgradeSelected()");
        console.log("   - forceUpgradeSelected() - bypasses CanUpgrade check");
        console.log("   - slowUpgradeSelected() - slow upgrades with long delays");
        console.log("   - simpleUpgradeSelected() - simple upgrades ignoring return values");
        console.log("   - getEntityInfo(instance)");
        console.log("   - getEntityStats()");
        console.log("   - debugUpgradeFailure(instanceIndex) - detailed debugging");
    }

    /**
     * Get current statistics
     */
    public getStats(): AutomationStats {
        const runtime = Date.now() - this.stats.startTime;
        console.log(`📊 Stats: Total: ${this.stats.totalInstances}, Selected: ${this.stats.selectedInstances}, Upgrades: ${this.stats.upgradesPerformed}, Runtime: ${runtime}ms`);
        return { ...this.stats };
    }

    /**
     * Force upgrade selected entities by bypassing CanUpgrade check
     */
    public forceUpgradeSelected(): number {
        try {
            console.log("⚡ Starting FORCE upgrade for selected entities (bypassing CanUpgrade)...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let selectedCount = 0;

            instances.forEach((instance, index) => {
                try {
                    if (!this.isValidInstance(instance)) {
                        return;
                    }

                    const isSelectedMethod = this.safeGetMethod(instance, "IsSelected");
                    if (!isSelectedMethod) return;

                    const isSelected = this.safeInvokeMethod(isSelectedMethod, [], `Instance ${index} IsSelected (force)`);
                    if (isSelected === null || !isSelected) return;

                    selectedCount++;
                    console.log(`⚡ FORCE upgrading selected entity ${index}`);

                    // Skip CanUpgrade check and try direct InstantUpgrade
                    const instantUpgradeMethod = this.safeGetMethod(instance, "InstantUpgrade");
                    if (instantUpgradeMethod) {
                        const getLevelMethod = this.safeGetMethod(instance, "GetLevel");

                        if (getLevelMethod) {
                            const levelBefore = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (before force)`);
                            console.log(`🔍 Instance ${index}: Level before force upgrade: ${levelBefore}`);

                            const upgradeResult = this.safeInvokeMethod(instantUpgradeMethod, [], `Instance ${index} FORCE InstantUpgrade`);

                            const levelAfter = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (after force)`);
                            console.log(`🔍 Instance ${index}: Level after force upgrade: ${levelAfter}`);

                            if (levelAfter && levelBefore && levelAfter > levelBefore) {
                                upgradedCount++;
                                console.log(`✅ Instance ${index}: FORCE upgrade successful! ${levelBefore} → ${levelAfter}`);
                            } else {
                                console.log(`❌ Instance ${index}: FORCE upgrade failed - level unchanged`);
                            }
                        }
                    }

                } catch (error) {
                    const errorMsg = error instanceof Error ? error.message : String(error);
                    console.log(`❌ Error force upgrading entity ${index}: ${errorMsg}`);
                }
            });

            console.log(`✅ Force upgrade complete! Selected: ${selectedCount}, Upgraded: ${upgradedCount} levels`);
            return upgradedCount;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Force upgrade failed: ${errorMsg}`);
            return 0;
        }
    }

    /**
     * Simple upgrade selected entities - ignores return values, just calls InstantUpgrade repeatedly
     */
    public simpleUpgradeSelected(): number {
        try {
            console.log("⚡ Starting SIMPLE upgrade for selected entities (ignoring return values)...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let selectedCount = 0;

            instances.forEach((instance, index) => {
                try {
                    if (!this.isValidInstance(instance)) {
                        return;
                    }

                    const isSelectedMethod = this.safeGetMethod(instance, "IsSelected");
                    if (!isSelectedMethod) return;

                    const isSelected = this.safeInvokeMethod(isSelectedMethod, [], `Instance ${index} IsSelected (simple)`);
                    if (isSelected === null || !isSelected) return;

                    selectedCount++;
                    console.log(`⚡ SIMPLE upgrading selected entity ${index}`);

                    // Get required methods
                    const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
                    const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
                    const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
                    const instantUpgradeMethod = this.safeGetMethod(instance, "InstantUpgrade");

                    if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod || !instantUpgradeMethod) {
                        console.log(`⚠️ Instance ${index}: Required methods not available`);
                        return;
                    }

                    let currentLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (simple start)`);
                    const maxLevel = this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], `Instance ${index} GetMaxUpgradeLevel (simple)`);

                    if (currentLevel === null || maxLevel === null) {
                        console.log(`⚠️ Instance ${index}: Failed to get level information`);
                        return;
                    }

                    console.log(`📊 Instance ${index}: Starting simple upgrade from level ${currentLevel}/${maxLevel}`);

                    // Simple approach: just call InstantUpgrade repeatedly until max level
                    const maxUpgrades = maxLevel - currentLevel;
                    for (let upgradeNum = 0; upgradeNum < maxUpgrades; upgradeNum++) {
                        console.log(`⚡ Instance ${index}: Simple upgrade ${upgradeNum + 1}/${maxUpgrades}`);

                        // Check if can still upgrade
                        const canUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${index} CanUpgrade(true) (simple ${upgradeNum + 1})`);
                        if (!canUpgrade) {
                            console.log(`📋 Instance ${index}: Cannot upgrade further (simple check)`);
                            break;
                        }

                        const levelBefore = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (before simple ${upgradeNum + 1})`);

                        // Just call InstantUpgrade - ignore return value since it's likely null
                        this.safeInvokeMethod(instantUpgradeMethod, [], `Instance ${index} SIMPLE InstantUpgrade ${upgradeNum + 1}`);

                        // Short delay to let upgrade process
                        const delay = 1000; // 1 second
                        const start = Date.now();
                        while (Date.now() - start < delay) { /* delay */ }

                        const levelAfter = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (after simple ${upgradeNum + 1})`);

                        if (levelAfter && levelBefore && levelAfter > levelBefore) {
                            upgradedCount++;
                            currentLevel = levelAfter;
                            console.log(`✅ Instance ${index}: Simple upgrade successful! ${levelBefore} → ${levelAfter}`);
                        } else if (levelAfter === levelBefore) {
                            console.log(`⚠️ Instance ${index}: Level unchanged (${levelBefore}), may need more time or reached limit`);
                            // Continue anyway - might just need more time
                        } else {
                            console.log(`❌ Instance ${index}: Unexpected level change (${levelBefore} → ${levelAfter})`);
                            break;
                        }

                        // Check if we've reached max level
                        if (currentLevel >= maxLevel) {
                            console.log(`🎯 Instance ${index}: Reached max level ${currentLevel}/${maxLevel}`);
                            break;
                        }
                    }

                    console.log(`⚡ Instance ${index}: Simple upgrade complete. Final level: ${currentLevel}/${maxLevel}`);

                } catch (error) {
                    const errorMsg = error instanceof Error ? error.message : String(error);
                    console.log(`❌ Error simple upgrading entity ${index}: ${errorMsg}`);
                }
            });

            console.log(`✅ Simple upgrade complete! Selected: ${selectedCount}, Upgraded: ${upgradedCount} levels`);
            return upgradedCount;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Simple upgrade failed: ${errorMsg}`);
            return 0;
        }
    }

    /**
     * Slow upgrade selected entities with extended delays to bypass anti-debugging
     */
    public slowUpgradeSelected(): number {
        try {
            console.log("🐌 Starting SLOW upgrade for selected entities (extended delays)...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let selectedCount = 0;

            instances.forEach((instance, index) => {
                try {
                    if (!this.isValidInstance(instance)) {
                        return;
                    }

                    const isSelectedMethod = this.safeGetMethod(instance, "IsSelected");
                    if (!isSelectedMethod) return;

                    const isSelected = this.safeInvokeMethod(isSelectedMethod, [], `Instance ${index} IsSelected (slow)`);
                    if (isSelected === null || !isSelected) return;

                    selectedCount++;
                    console.log(`🐌 SLOW upgrading selected entity ${index}`);

                    // Get required methods
                    const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
                    const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
                    const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
                    const instantUpgradeMethod = this.safeGetMethod(instance, "InstantUpgrade");

                    if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod || !instantUpgradeMethod) {
                        console.log(`⚠️ Instance ${index}: Required methods not available`);
                        return;
                    }

                    // Check upgrade capability
                    const canUpgradeAlt = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${index} CanUpgrade(true) (slow)`);
                    if (!canUpgradeAlt) {
                        console.log(`📋 Instance ${index}: Cannot be upgraded (slow check)`);
                        return;
                    }

                    let currentLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (slow)`);
                    const maxLevel = this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], `Instance ${index} GetMaxUpgradeLevel (slow)`);

                    console.log(`📊 Instance ${index}: Starting slow upgrade from level ${currentLevel}/${maxLevel}`);

                    // Slow upgrade loop with very long delays
                    let upgradeAttempts = 0;
                    const maxUpgradeAttempts = maxLevel - currentLevel; // Try to reach max level

                    while (currentLevel < maxLevel && upgradeAttempts < maxUpgradeAttempts) {
                        console.log(`🐌 Instance ${index}: Slow upgrade attempt ${upgradeAttempts + 1}/${maxUpgradeAttempts}`);

                        // Very long delay before each upgrade attempt
                        const delay = 5000; // 5 seconds between each upgrade
                        console.log(`⏳ Waiting ${delay}ms to avoid anti-debug detection...`);
                        const start = Date.now();
                        while (Date.now() - start < delay) { /* 5 second delay */ }

                        // Check if still can upgrade
                        const stillCanUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${index} CanUpgrade(true) (slow attempt ${upgradeAttempts + 1})`);
                        if (!stillCanUpgrade) {
                            console.log(`⚠️ Instance ${index}: Can no longer upgrade, stopping`);
                            break;
                        }

                        const levelBefore = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (before slow upgrade ${upgradeAttempts + 1})`);

                        // Attempt upgrade with extended retry logic
                        let upgradeSuccess = false;
                        for (let retryAttempt = 0; retryAttempt < 5; retryAttempt++) {
                            console.log(`🐌 Instance ${index}: InstantUpgrade attempt ${retryAttempt + 1}/5`);

                            const upgradeResult = this.safeInvokeMethod(instantUpgradeMethod, [], `Instance ${index} SLOW InstantUpgrade (retry ${retryAttempt + 1})`);

                            if (upgradeResult !== null) {
                                upgradeSuccess = true;
                                break;
                            }

                            // Wait between retry attempts
                            if (retryAttempt < 4) {
                                const retryDelay = 3000; // 3 seconds between retries
                                console.log(`⏳ Retry failed, waiting ${retryDelay}ms before next retry...`);
                                const retryStart = Date.now();
                                while (Date.now() - retryStart < retryDelay) { /* retry delay */ }
                            }
                        }

                        if (!upgradeSuccess) {
                            console.log(`❌ Instance ${index}: All slow upgrade retries failed, stopping`);
                            break;
                        }

                        const levelAfter = this.safeInvokeMethod(getLevelMethod, [], `Instance ${index} GetLevel (after slow upgrade ${upgradeAttempts + 1})`);

                        if (levelAfter && levelBefore && levelAfter > levelBefore) {
                            upgradedCount++;
                            currentLevel = levelAfter;
                            console.log(`✅ Instance ${index}: Slow upgrade successful! ${levelBefore} → ${levelAfter}`);
                        } else {
                            console.log(`⚠️ Instance ${index}: Level didn't change (${levelBefore} → ${levelAfter}), stopping`);
                            break;
                        }

                        upgradeAttempts++;
                    }

                    console.log(`🐌 Instance ${index}: Slow upgrade complete. Final level: ${currentLevel}/${maxLevel}`);

                } catch (error) {
                    const errorMsg = error instanceof Error ? error.message : String(error);
                    console.log(`❌ Error slow upgrading entity ${index}: ${errorMsg}`);
                }
            });

            console.log(`✅ Slow upgrade complete! Selected: ${selectedCount}, Upgraded: ${upgradedCount} levels`);
            return upgradedCount;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Slow upgrade failed: ${errorMsg}`);
            return 0;
        }
    }

    /**
     * Debug upgrade failure for a specific instance
     */
    public debugUpgradeFailure(instanceIndex: number): void {
        try {
            const instances = this.getAllInstances();
            if (instanceIndex >= instances.length) {
                console.log(`❌ Instance ${instanceIndex} not found (max: ${instances.length - 1})`);
                return;
            }

            const instance = instances[instanceIndex];
            console.log(`🔍 Debugging upgrade failure for instance ${instanceIndex}...`);

            if (!this.isValidInstance(instance)) {
                console.log(`❌ Instance ${instanceIndex} is invalid`);
                return;
            }

            // Get all available methods for debugging
            console.log(`📋 Available methods on instance ${instanceIndex}:`);
            try {
                const instanceClass = instance.class;
                const methods = instanceClass.methods;
                methods.slice(0, 20).forEach((method, idx) => {
                    console.log(`   ${idx}: ${method.name}`);
                });
            } catch (error) {
                console.log(`   Could not enumerate methods: ${error}`);
            }

            // Test all upgrade-related methods
            const methodsToTest = [
                "IsSelected", "CanUpgrade", "GetLevel", "GetMaxLevel", "GetMaxUpgradeLevel",
                "InstantUpgrade", "GetUpgradeCost", "GetUpgradeTime", "IsUpgrading", "IsBusy",
                "GetState", "CanCollect", "IsCollecting", "GetResourceType"
            ];

            methodsToTest.forEach(methodName => {
                const method = this.safeGetMethod(instance, methodName);
                if (method) {
                    console.log(`✅ Method ${methodName} is available`);

                    // Try to call methods that don't need parameters
                    if (["IsSelected", "GetLevel", "GetMaxLevel", "GetMaxUpgradeLevel", "IsUpgrading", "IsBusy", "GetState"].includes(methodName)) {
                        const result = this.safeInvokeMethod(method, [], `Debug ${methodName}`);
                        console.log(`   ${methodName}() = ${result}`);
                    }
                } else {
                    console.log(`❌ Method ${methodName} not found`);
                }
            });

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Debug failed: ${errorMsg}`);
        }
    }

    /**
     * List available assemblies for debugging
     */
    private listAvailableAssemblies(): void {
        try {
            console.log("💡 Available assemblies:");
            const assemblies = Il2Cpp.domain.assemblies;
            assemblies.slice(0, 10).forEach((assembly, index) => {
                console.log(`   ${index}: ${assembly.name}`);
            });
        } catch (error) {
            console.log("   Could not enumerate assemblies");
        }
    }

    /**
     * List available classes for debugging
     */
    private listAvailableClasses(): void {
        if (!this.assemblyImage) return;

        try {
            console.log("💡 Available classes (first 10):");
            const classes = this.assemblyImage.classes;
            for (let i = 0; i < Math.min(10, classes.length); i++) {
                console.log(`   ${i}: ${classes[i].name}`);
            }

            // Look for classes containing "Entity" or "Controller"
            const entityClasses = classes.filter(cls =>
                cls.name.toLowerCase().includes('entity') ||
                cls.name.toLowerCase().includes('controller')
            );

            if (entityClasses.length > 0) {
                console.log("🔍 Found entity/controller related classes:");
                entityClasses.forEach((cls, index) => {
                    console.log(`   ${index}: ${cls.name}`);
                });
            }
        } catch (error) {
            console.log("   Could not enumerate classes");
        }
    }
}

// Script execution entry point
Il2Cpp.perform(async () => {
    console.log("🚀 EntityController Hook - Il2Cpp bridge context established");

    try {
        // Wait for game to be fully loaded
        await new Promise(resolve => setTimeout(resolve, 5000));

        const hook = new EntityControllerHook();
        const initialized = await hook.initialize();

        if (initialized) {
            console.log("✅ EntityController hook ready!");

            // Make hook instance available globally for debugging
            try {
                (globalThis as any).entityHook = hook;
                console.log("✅ Hook instance available as 'entityHook'");
            } catch (error) {
                console.log(`⚠️ Could not assign to globalThis: ${error}`);
                try {
                    (global as any).entityHook = hook;
                    console.log("✅ Hook instance available as 'entityHook' (via global)");
                } catch (globalError) {
                    console.log(`⚠️ Could not assign to global either: ${globalError}`);
                }
            }

            // Test global function accessibility
            setTimeout(() => {
                console.log("🧪 Testing global function accessibility...");
                try {
                    if (typeof (globalThis as any).getAllEntityInstances === 'function') {
                        console.log("✅ getAllEntityInstances is accessible globally");
                    } else {
                        console.log("❌ getAllEntityInstances is not accessible globally");
                    }
                } catch (testError) {
                    console.log(`⚠️ Global function test failed: ${testError}`);
                }
            }, 1000);

            // Optional: Start monitoring
            console.log("💡 Use autoUpgradeSelected() to upgrade selected entities");
            console.log("💡 Use entityHook.getAllInstances() if global functions don't work");
        } else {
            console.log("❌ Failed to initialize EntityController hook");
        }

    } catch (error) {
        console.log(`❌ Fatal error: ${error}`);
    }
});
