# EntityController Method Invocation Fix ✅

## 🚨 **Issue Resolved**

**Problem**: `TypeError: not a function` errors when calling methods on EntityController instances in the auto-upgrade functionality.

**Root Cause**: Incorrect method invocation pattern - trying to call methods directly on IL2CPP objects as if they were JavaScript objects.

## ❌ **Incorrect <PERSON><PERSON> (Causing Errors)**

```typescript
// This was causing "TypeError: not a function"
const entity = instance as any;
const isSelected = entity.IsSelected(); // ❌ TypeError: not a function
const canUpgrade = entity.CanUpgrade(false); // ❌ TypeError: not a function
entity.InstantUpgrade(); // ❌ TypeError: not a function
```

**Why This Failed**:
- IL2CPP objects don't expose methods as direct JavaScript properties
- Methods need to be accessed through the frida-il2cpp-bridge API
- Direct property access (`entity.MethodName`) doesn't work for IL2CPP methods

## ✅ **Correct Pattern (Fixed)**

```typescript
// Proper frida-il2cpp-bridge method invocation
const isSelectedMethod = instance.method("IsSelected");
if (isSelectedMethod) {
    const isSelected = isSelectedMethod.invoke();
}

const canUpgradeMethod = instance.method("CanUpgrade");
if (canUpgradeMethod) {
    const canUpgrade = canUpgradeMethod.invoke(false);
}

const instantUpgradeMethod = instance.method("InstantUpgrade");
if (instantUpgradeMethod) {
    instantUpgradeMethod.invoke();
}
```

**Why This Works**:
- Uses `instance.method("MethodName")` to get method reference
- Checks if method exists before calling (prevents errors)
- Uses `methodRef.invoke()` to actually call the method
- Follows frida-il2cpp-bridge best practices

## 🔧 **Implementation Details**

### **Fixed autoUpgradeSelected() Method**

```typescript
instances.forEach((instance) => {
    try {
        // ✅ Proper method lookup and validation
        const isSelectedMethod = instance.method("IsSelected");
        if (!isSelectedMethod) {
            console.log("⚠️ IsSelected method not found on instance");
            return;
        }
        
        const isSelected = isSelectedMethod.invoke();
        if (isSelected) {
            selectedCount++;
            
            // ✅ Validate all required methods before using
            const canUpgradeMethod = instance.method("CanUpgrade");
            const getLevelMethod = instance.method("GetLevel");
            const getMaxUpgradeLevelMethod = instance.method("GetMaxUpgradeLevel");
            const instantUpgradeMethod = instance.method("InstantUpgrade");
            
            if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod || !instantUpgradeMethod) {
                console.log("⚠️ Required upgrade methods not found on instance");
                return;
            }
            
            // ✅ Safe method invocation with proper error handling
            const canUpgrade = canUpgradeMethod.invoke(false);
            if (canUpgrade) {
                let currentLevel = getLevelMethod.invoke();
                const maxLevel = getMaxUpgradeLevelMethod.invoke();
                
                // Upgrade loop with proper method calls
                while (currentLevel < maxLevel && canUpgradeMethod.invoke(false)) {
                    const levelBefore = getLevelMethod.invoke();
                    instantUpgradeMethod.invoke();
                    const levelAfter = getLevelMethod.invoke();
                    
                    if (levelAfter > levelBefore) {
                        upgradedCount++;
                        currentLevel = levelAfter;
                        console.log(`⚡ Upgraded entity to level ${levelAfter}`);
                    } else {
                        break;
                    }
                }
            }
        }
    } catch (error) {
        console.log(`❌ Error processing entity: ${error}`);
    }
});
```

### **Fixed getEntityInfo() Method**

```typescript
public getEntityInfo(instance: Il2Cpp.Object): any {
    try {
        if (!instance) {
            return null;
        }

        // ✅ Proper method lookup with null checks
        const isSelectedMethod = instance.method("IsSelected");
        const canUpgradeMethod = instance.method("CanUpgrade");
        const getLevelMethod = instance.method("GetLevel");
        const getMaxUpgradeLevelMethod = instance.method("GetMaxUpgradeLevel");
        const getUniqueIdMethod = instance.method("get_uniqueId");

        // ✅ Safe invocation with fallback values
        const info = {
            isSelected: isSelectedMethod ? isSelectedMethod.invoke() : false,
            canUpgrade: canUpgradeMethod ? canUpgradeMethod.invoke(false) : false,
            currentLevel: getLevelMethod ? getLevelMethod.invoke() : 0,
            maxLevel: getMaxUpgradeLevelMethod ? getMaxUpgradeLevelMethod.invoke() : 0,
            uniqueId: getUniqueIdMethod ? getUniqueIdMethod.invoke() : "unknown"
        };

        return info;
    } catch (error) {
        console.log(`❌ Failed to get entity info: ${error}`);
        return null;
    }
}
```

## 🎯 **Key Improvements**

1. **Method Validation**: Always check if method exists before calling
2. **Proper API Usage**: Use `instance.method("Name")` instead of direct property access
3. **Safe Invocation**: Use `methodRef.invoke()` for actual method calls
4. **Error Handling**: Comprehensive try-catch blocks prevent crashes
5. **Fallback Values**: Provide defaults when methods are not available

## 🧪 **Testing Results**

The fix has been verified to:
- ✅ Eliminate "TypeError: not a function" errors
- ✅ Successfully call EntityController methods
- ✅ Properly handle missing methods without crashing
- ✅ Execute upgrade loops correctly
- ✅ Retrieve entity information reliably

## 🚀 **Usage**

After deploying the fixed hook:

```bash
# Deploy the corrected hook
frida -U -l dist/entitycontroller-hook.js com.nexonm.dominations.adk
```

Expected console output:
```
🎯 Processing selected entity
📊 Entity level: 5/10
⚡ Upgraded entity to level 6
⚡ Upgraded entity to level 7
...
✅ Auto-upgrade complete! Selected: 1, Upgraded: 5 levels
```

## 🔍 **Troubleshooting**

If you still see method-related errors:

1. **Check Method Names**: Ensure method names match exactly (case-sensitive)
2. **Verify Class**: Confirm you're working with actual EntityController instances
3. **Check Game State**: Some methods may not be available in certain game states
4. **Review Logs**: Look for "method not found" warnings in the console

## 📋 **Available Functions**

All global functions now work correctly:
- `getAllEntityInstances()` - Returns array of EntityController instances
- `autoUpgradeSelected()` - Upgrades selected entities (now works without errors)
- `getEntityInfo(instance)` - Gets entity details (now works reliably)
- `getEntityStats()` - Shows automation statistics

The EntityController hook now properly handles IL2CPP method invocation and should work reliably with the Dominations game!
