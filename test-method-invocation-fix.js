/**
 * Test script to verify the method invocation fix for <PERSON><PERSON>tyControll<PERSON>
 * This addresses the "TypeError: not a function" errors
 */

console.log("🧪 Testing Method Invocation Fix for EntityController...");
console.log("🎯 Addressing: TypeError: not a function errors");
console.log("");

// Test 1: Verify the correct method invocation pattern
console.log("📋 Test 1: Verifying correct method invocation pattern...");

// Simulate the corrected pattern we're now using
function testCorrectMethodInvocation() {
    console.log("✅ Testing corrected method invocation pattern:");
    
    // Mock Il2Cpp object with method() function
    const mockInstance = {
        method: function(methodName) {
            console.log(`   - Looking up method: ${methodName}`);
            
            // Simulate method lookup
            const mockMethods = {
                "IsSelected": { invoke: () => true },
                "CanUpgrade": { invoke: (useAlt) => true },
                "GetLevel": { invoke: () => 5 },
                "GetMaxUpgradeLevel": { invoke: () => 10 },
                "InstantUpgrade": { invoke: () => undefined },
                "get_uniqueId": { invoke: () => 12345 }
            };
            
            if (mockMethods[methodName]) {
                console.log(`   ✅ Found method: ${methodName}`);
                return mockMethods[methodName];
            } else {
                console.log(`   ❌ Method not found: ${methodName}`);
                return null;
            }
        }
    };
    
    // Test the corrected invocation pattern
    try {
        console.log("🔧 Testing method lookup and invocation...");
        
        const isSelectedMethod = mockInstance.method("IsSelected");
        if (isSelectedMethod) {
            const result = isSelectedMethod.invoke();
            console.log(`   ✅ IsSelected() = ${result}`);
        }
        
        const canUpgradeMethod = mockInstance.method("CanUpgrade");
        if (canUpgradeMethod) {
            const result = canUpgradeMethod.invoke(false);
            console.log(`   ✅ CanUpgrade(false) = ${result}`);
        }
        
        const getLevelMethod = mockInstance.method("GetLevel");
        if (getLevelMethod) {
            const result = getLevelMethod.invoke();
            console.log(`   ✅ GetLevel() = ${result}`);
        }
        
        console.log("✅ All method invocations successful!");
        
    } catch (error) {
        console.log(`❌ Method invocation test failed: ${error}`);
    }
}

testCorrectMethodInvocation();

// Test 2: Compare incorrect vs correct patterns
console.log("\n📋 Test 2: Comparing incorrect vs correct patterns...");

console.log("❌ INCORRECT pattern (causes TypeError: not a function):");
console.log("   const entity = instance as any;");
console.log("   const isSelected = entity.IsSelected(); // TypeError if IsSelected is not directly accessible");

console.log("\n✅ CORRECT pattern (works reliably):");
console.log("   const isSelectedMethod = instance.method('IsSelected');");
console.log("   if (isSelectedMethod) {");
console.log("       const isSelected = isSelectedMethod.invoke();");
console.log("   }");

// Test 3: Verify error handling pattern
console.log("\n📋 Test 3: Testing error handling pattern...");

function testErrorHandling() {
    console.log("🔧 Testing error handling for missing methods...");
    
    const mockInstanceWithMissingMethods = {
        method: function(methodName) {
            // Simulate some methods missing
            if (methodName === "NonExistentMethod") {
                return null;
            }
            return { invoke: () => "success" };
        }
    };
    
    try {
        // Test handling of missing method
        const missingMethod = mockInstanceWithMissingMethods.method("NonExistentMethod");
        if (!missingMethod) {
            console.log("✅ Correctly detected missing method");
            console.log("✅ Error handling prevents TypeError");
        }
        
        // Test handling of existing method
        const existingMethod = mockInstanceWithMissingMethods.method("ExistingMethod");
        if (existingMethod) {
            const result = existingMethod.invoke();
            console.log(`✅ Existing method works: ${result}`);
        }
        
    } catch (error) {
        console.log(`❌ Error handling test failed: ${error}`);
    }
}

testErrorHandling();

// Test 4: Verify the upgrade loop logic
console.log("\n📋 Test 4: Testing upgrade loop logic...");

function testUpgradeLoop() {
    console.log("🔧 Testing upgrade loop with proper method invocation...");
    
    let mockLevel = 5;
    const maxLevel = 10;
    let upgradeCount = 0;
    
    const mockUpgradeInstance = {
        method: function(methodName) {
            const methods = {
                "GetLevel": { 
                    invoke: () => mockLevel 
                },
                "GetMaxUpgradeLevel": { 
                    invoke: () => maxLevel 
                },
                "CanUpgrade": { 
                    invoke: (useAlt) => mockLevel < maxLevel 
                },
                "InstantUpgrade": { 
                    invoke: () => {
                        if (mockLevel < maxLevel) {
                            mockLevel++;
                            upgradeCount++;
                        }
                    }
                }
            };
            return methods[methodName] || null;
        }
    };
    
    try {
        console.log(`   Starting level: ${mockLevel}/${maxLevel}`);
        
        // Simulate the corrected upgrade loop
        const getLevelMethod = mockUpgradeInstance.method("GetLevel");
        const getMaxUpgradeLevelMethod = mockUpgradeInstance.method("GetMaxUpgradeLevel");
        const canUpgradeMethod = mockUpgradeInstance.method("CanUpgrade");
        const instantUpgradeMethod = mockUpgradeInstance.method("InstantUpgrade");
        
        if (getLevelMethod && getMaxUpgradeLevelMethod && canUpgradeMethod && instantUpgradeMethod) {
            let currentLevel = getLevelMethod.invoke();
            const maxUpgradeLevel = getMaxUpgradeLevelMethod.invoke();
            
            let attempts = 0;
            const maxAttempts = 10;
            
            while (currentLevel < maxUpgradeLevel && 
                   canUpgradeMethod.invoke(false) && 
                   attempts < maxAttempts) {
                
                const levelBefore = getLevelMethod.invoke();
                instantUpgradeMethod.invoke();
                const levelAfter = getLevelMethod.invoke();
                
                if (levelAfter > levelBefore) {
                    currentLevel = levelAfter;
                    console.log(`   ✅ Upgraded to level ${levelAfter}`);
                } else {
                    console.log("   ⚠️ Level didn't change, stopping");
                    break;
                }
                
                attempts++;
            }
            
            console.log(`✅ Upgrade loop completed: ${upgradeCount} upgrades performed`);
            console.log(`✅ Final level: ${mockLevel}/${maxLevel}`);
            
        } else {
            console.log("❌ Required methods not found");
        }
        
    } catch (error) {
        console.log(`❌ Upgrade loop test failed: ${error}`);
    }
}

testUpgradeLoop();

console.log("\n🎯 Method Invocation Fix Tests Completed!");
console.log("📋 Summary of fixes:");
console.log("   ✅ Use instance.method('MethodName') to get method reference");
console.log("   ✅ Check if method exists before calling");
console.log("   ✅ Use methodRef.invoke() to call the method");
console.log("   ✅ Proper error handling for missing methods");
console.log("   ✅ Safe upgrade loop with method validation");
console.log("");
console.log("💡 This should resolve:");
console.log("   - TypeError: not a function errors");
console.log("   - Failed method calls on EntityController instances");
console.log("   - Auto-upgrade functionality issues");
console.log("");
console.log("🚀 Ready to test with: frida -U -l dist/entitycontroller-hook.js com.nexonm.dominations.adk");
console.log("🔍 Expected results:");
console.log("   - No more 'TypeError: not a function' errors");
console.log("   - Successful entity method calls");
console.log("   - Working auto-upgrade functionality");
console.log("   - Proper entity information retrieval");
